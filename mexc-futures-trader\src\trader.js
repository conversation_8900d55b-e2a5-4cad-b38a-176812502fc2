const { chromium } = require('playwright');
const axios = require('axios');

class MexcFuturesTrader {
    constructor(port = 9223) { // Changed to single browser on port 9223
        this.browser = null;
        this.page = null;
        this.port = port;
        this.telegramBotToken = '**********************************************';
        this.telegramChatId = '243673531';
        this.lastBalance = null;
        this.lastBalanceUpdate = null;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async executeOrder(orderType, quantity = '0.3600') {
        const startTime = Date.now();
        console.log(`🎯 EXECUTING ${orderType.toUpperCase()} ORDER...`);
        console.log(`💰 Quantity: ${quantity} USDT`);
        
        try {
            // Check current page (don't navigate if already on MEXC)
            const url = this.page.url();
            if (url.includes('mexc.com')) {
                console.log('✅ Already on MEXC page');
            } else {
                console.log('🌐 Navigating to TRU_USDT...');
                await this.page.goto('https://www.mexc.com/futures/TRU_USDT');
                await this.page.waitForTimeout(1000); // Reduced from 2000ms
            }

            // Step 1: Select appropriate tab (Open or Close) - SINGLE BROWSER APPROACH
            await this.selectTab(orderType);

            // Check for login requirement before proceeding
            const loginRequired = await this.checkLoginRequired();
            if (loginRequired) {
                await this.sendTelegramAlert('🚨 MEXC Login Required',
                    `MEXC session expired. Please login manually.\nPort: ${this.port}\nOrder: ${orderType}`);
                throw new Error('MEXC login required. Session expired.');
            }

            // Fill quantity with error handling
            await this.fillQuantityWithErrorHandling(orderType, quantity);

            // Click order button with error handling
            await this.clickOrderButtonWithErrorHandling(orderType);

            // Quick error check for insufficient closeable quantity (no popup waiting)
            await this.quickErrorCheck(orderType);

            const executionTime = Date.now() - startTime;
            // Quick verification - reduced timeout for speed
            const verified = await this.quickVerifySuccess(orderType);

            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);
            console.log(`🎉 Verified: ${verified ? '✅ YES' : '❌ NO'}`);

            return {
                success: verified,
                executionTime,
                verified,
                orderType,
                quantity,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            // Handle SKIP_TRADE errors (insufficient closeable quantity, etc.)
            if (error.message.startsWith('SKIP_TRADE:')) {
                const skipReason = error.message.replace('SKIP_TRADE: ', '');
                console.log(`⏭️ Skipping trade: ${skipReason}`);

                return {
                    success: false,
                    skipped: true,
                    skipReason: skipReason,
                    executionTime,
                    verified: false,
                    orderType,
                    quantity,
                    targetAchieved: false,
                    timestamp: new Date().toISOString()
                };
            }

            console.error(`❌ ${orderType} failed: ${error.message}`);

            // Emergency recovery for other errors
            console.log('🚨 Emergency recovery...');
            await this.aggressiveCleanup();
            await new Promise(resolve => setTimeout(resolve, 1000));

            const verified = await this.verifySuccess(orderType);
            if (verified) {
                console.log('✅ Emergency recovery successful!');
                return {
                    success: true,
                    executionTime,
                    verified: true,
                    orderType,
                    quantity,
                    emergencyRecovery: true,
                    timestamp: new Date().toISOString()
                };
            }
            
            return { 
                success: false, 
                executionTime, 
                error: error.message, 
                orderType,
                quantity,
                timestamp: new Date().toISOString()
            };
        }
    }

    async fillQuantityWithErrorHandling(orderType, quantity) {
        console.log(`🔢 Filling quantity: ${quantity}...`);
        
        try {
            // First attempt - no cleanup
            await this.fillQuantity(quantity);
            return;
        } catch (error) {
            console.log('⚠️ First quantity attempt failed, clearing quantity fields...');
            
            try {
                // First error: Clean quantity only
                await this.clearQuantityFields();
                await this.fillQuantity(quantity);
                return;
            } catch (secondError) {
                console.log('⚠️ Second quantity attempt failed, closing popups...');
                
                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.fillQuantity(quantity);
            }
        }
    }

    async clickOrderButtonWithErrorHandling(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);
        
        try {
            // First attempt - no cleanup
            await this.clickOrderButton(orderType);
            return;
        } catch (error) {
            console.log('⚠️ First button click failed, clearing quantity fields...');
            
            try {
                // First error: Clean quantity only (might be quantity issue)
                await this.clearQuantityFields();
                await this.fillQuantity();
                await this.clickOrderButton(orderType);
                return;
            } catch (secondError) {
                console.log('⚠️ Second button click failed, closing popups...');
                
                // Second error: Close popups then try again
                await this.closePersistentPopups();
                await this.page.waitForTimeout(500);
                await this.clickOrderButton(orderType);
            }
        }
    }

    async selectTab(orderType) {
        try {
            console.log(`📋 Selecting tab for ${orderType}...`);

            let targetTabSelector, targetTabName;
            if (orderType.includes('Open')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
                targetTabName = 'Open';
            } else if (orderType.includes('Close')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
                targetTabName = 'Close';
            } else {
                throw new Error(`Unknown order type: ${orderType}`);
            }

            // Check if we're already on the correct tab (performance optimization)
            const isCurrentTabActive = await this.isTabActive(targetTabSelector);
            if (isCurrentTabActive) {
                console.log(`✅ Already on ${targetTabName} tab, skipping click`);
                return;
            }

            console.log(`🔄 Switching to ${targetTabName} tab...`);

            // Wait for tab to be visible and click it
            await this.page.waitForSelector(targetTabSelector, { timeout: 3000 });
            await this.page.click(targetTabSelector);

            // Wait a moment for tab content to load
            await this.page.waitForTimeout(500);

            // Verify the tab switch was successful
            const switchSuccessful = await this.isTabActive(targetTabSelector);
            if (!switchSuccessful) {
                throw new Error(`Failed to switch to ${targetTabName} tab`);
            }

            console.log(`✅ Successfully switched to ${targetTabName} tab`);
        } catch (error) {
            console.error('❌ Failed to select tab:', error.message);
            throw error;
        }
    }

    async isTabActive(tabSelector) {
        try {
            // Check if the tab has active/selected class or attribute
            const tabElement = this.page.locator(tabSelector).first();

            // Wait for element to be visible
            if (!await tabElement.isVisible({ timeout: 1000 })) {
                return false;
            }

            // Check various ways a tab might indicate it's active
            const activeChecks = [
                // Check for active class
                async () => {
                    const className = await tabElement.getAttribute('class');
                    return className && (className.includes('active') || className.includes('selected'));
                },
                // Check for aria-selected attribute
                async () => {
                    const ariaSelected = await tabElement.getAttribute('aria-selected');
                    return ariaSelected === 'true';
                },
                // Check parent element for active state
                async () => {
                    const parent = tabElement.locator('..');
                    const parentClass = await parent.getAttribute('class');
                    return parentClass && (parentClass.includes('active') || parentClass.includes('selected'));
                },
                // Check if the tab content is visible (Open/Close specific content)
                async () => {
                    if (tabSelector.includes('open')) {
                        // Check for Open Long/Short buttons
                        const openButtons = this.page.locator('button:has-text("Open Long"), button:has-text("Open Short")');
                        return await openButtons.first().isVisible({ timeout: 500 });
                    } else {
                        // Check for Close Long/Short buttons
                        const closeButtons = this.page.locator('button:has-text("Close Long"), button:has-text("Close Short")');
                        return await closeButtons.first().isVisible({ timeout: 500 });
                    }
                }
            ];

            // Try each check method
            for (const check of activeChecks) {
                try {
                    if (await check()) {
                        return true;
                    }
                } catch (error) {
                    // Continue to next check
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Tab active check failed:', error.message);
            return false;
        }
    }

    async forceTabSwitch(orderType) {
        console.log(`🔧 Force switching tab for ${orderType}...`);

        try {
            let targetTabSelector;
            if (orderType.includes('Open')) {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-open"]';
            } else {
                targetTabSelector = 'span[data-testid="contract-trade-order-form-tab-close"]';
            }

            // Multiple click attempts with different strategies
            const clickStrategies = [
                // Strategy 1: Direct click
                async () => {
                    await this.page.click(targetTabSelector);
                },
                // Strategy 2: Click with force
                async () => {
                    await this.page.click(targetTabSelector, { force: true });
                },
                // Strategy 3: Click parent element
                async () => {
                    const parent = this.page.locator(targetTabSelector).locator('..');
                    await parent.click();
                },
                // Strategy 4: JavaScript click
                async () => {
                    await this.page.evaluate((selector) => {
                        const element = document.querySelector(selector);
                        if (element) element.click();
                    }, targetTabSelector);
                }
            ];

            for (let i = 0; i < clickStrategies.length; i++) {
                try {
                    await clickStrategies[i]();
                    await this.page.waitForTimeout(300);

                    // Check if switch was successful
                    if (await this.isTabActive(targetTabSelector)) {
                        console.log(`✅ Force tab switch successful with strategy ${i + 1}`);
                        return true;
                    }
                } catch (error) {
                    console.log(`⚠️ Force tab switch strategy ${i + 1} failed:`, error.message);
                }
            }

            throw new Error('All force tab switch strategies failed');
        } catch (error) {
            console.error('❌ Force tab switch failed:', error.message);
            throw error;
        }
    }

    async fillQuantity(quantity = '0.3600') {
        // Check if we're in close mode and handle differently
        const isCloseMode = await this.isInCloseMode();
        if (isCloseMode) {
            console.log('🔄 Detected close mode - using close-specific quantity strategies');
            return await this.fillQuantityForClose(quantity);
        }

        const strategies = [
            // Strategy 1: XPath following Quantity(USDT) - Fast version
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 500 })) {
                    // Try direct fill first (fastest)
                    await input.click();
                    await input.fill(quantity);

                    const value = await input.inputValue();
                    if (value === quantity) {
                        return true;
                    }

                    // If direct fill failed, clear and retry
                    await this.clearInputField(input);
                    await input.click();
                    await input.fill(quantity);

                    const retryValue = await input.inputValue();
                    return retryValue === quantity;
                }
                return false;
            },

            // Strategy 2: XPath following Quantity - Fast version
            async () => {
                const input = this.page.locator('text=Quantity >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    const type = await input.getAttribute('type');
                    if (type !== 'checkbox') {
                        await input.click();
                        await input.fill(quantity);

                        const value = await input.inputValue();
                        if (value === quantity) {
                            return true;
                        }

                        await this.clearInputField(input);
                        await input.click();
                        await input.fill(quantity);

                        const retryValue = await input.inputValue();
                        return retryValue === quantity;
                    }
                }
                return false;
            },

            // Strategy 3: Direct text/number inputs - Fast version
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                await input.click();
                                await input.fill(quantity);

                                const value = await input.inputValue();
                                if (value === quantity) {
                                    return true;
                                }

                                await this.clearInputField(input);
                                await input.click();
                                await input.fill(quantity);

                                const retryValue = await input.inputValue();
                                if (retryValue === quantity) {
                                    return true;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return false;
            }
        ];

        for (let i = 0; i < strategies.length; i++) {
            try {
                if (await strategies[i]()) {
                    console.log(`✅ Quantity filled using strategy ${i + 1}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field');
    }

    async isInCloseMode() {
        try {
            // Check if Close tab is active by looking for the active class
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
            const hasActiveClass = await closeTab.getAttribute('class');
            const isActive = hasActiveClass && (hasActiveClass.includes('active') || hasActiveClass.includes('handle_active'));

            console.log(`🔍 Close mode check: ${isActive ? 'YES' : 'NO'} (class: ${hasActiveClass})`);
            return isActive;
        } catch (error) {
            console.log('⚠️ Close mode detection failed, assuming open mode');
            return false;
        }
    }

    async fillQuantityForClose(quantity = '0.3600') {
        console.log(`🔢 Filling quantity for close mode: ${quantity}...`);

        const closeStrategies = [
            // Strategy 1: Fast force click on ant-input in trading area
            async () => {
                const inputs = await this.page.locator('input.ant-input[type="text"]').all();
                for (const input of inputs) {
                    try {
                        if (await input.isVisible({ timeout: 100 })) {
                            const boundingBox = await input.boundingBox();
                            if (boundingBox && boundingBox.y > 200) {
                                const className = await input.getAttribute('class');

                                if (className && !className.includes('search') && !className.includes('filter')) {
                                    console.log(`🎯 Fast force click on: ${className}`);

                                    await input.click({ force: true });
                                    await input.fill(quantity);

                                    const value = await input.inputValue();
                                    if (value === quantity) {
                                        console.log(`✅ Close quantity filled using fast force click`);
                                        return true;
                                    }

                                    await this.clearInputField(input);
                                    await input.click({ force: true });
                                    await input.fill(quantity);

                                    const retryValue = await input.inputValue();
                                    if (retryValue === quantity) {
                                        console.log(`✅ Close quantity filled after clearing`);
                                        return true;
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                return false;
            },

            // Strategy 2: XPath method (fallback)
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    console.log('✅ Found Quantity(USDT) field in close mode');

                    await input.click({ force: true });
                    await input.fill(quantity);

                    const value = await input.inputValue();
                    if (value === quantity) {
                        return true;
                    }

                    await this.clearInputField(input);
                    await input.click({ force: true });
                    await input.fill(quantity);

                    const retryValue = await input.inputValue();
                    return retryValue === quantity;
                }
                return false;
            }
        ];

        for (let i = 0; i < closeStrategies.length; i++) {
            try {
                if (await closeStrategies[i]()) {
                    console.log(`✅ Close quantity strategy ${i + 1} succeeded`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Close strategy ${i + 1} failed:`, error.message);
                continue;
            }
        }

        throw new Error('Could not fill quantity field in close mode');
    }

    async clickOrderButton(orderType) {
        console.log(`📊 Clicking ${orderType} button...`);

        // Check if this is a close order and if there are positions to close
        if (orderType.includes('Close')) {
            const hasPositions = await this.checkForOpenPositions();
            if (!hasPositions) {
                throw new Error(`No open positions to close. Cannot execute ${orderType}.`);
            }
        }

        const buttonMap = {
            'Open Long': [
                'button:has-text("Open Long")',
                'text=Open Long',
                '.buy-button',
                'button[class*="buy"]',
                'button[class*="long"]'
            ],
            'Open Short': [
                'button:has-text("Open Short")',
                'text=Open Short',
                '.sell-button',
                'button[class*="sell"]',
                'button[class*="short"]'
            ],
            'Close Long': [
                'button:has-text("Close Long")',
                'text=Close Long',
                'button:has-text("Close")',
                '.close-long-button',
                'button[class*="close"][class*="long"]'
            ],
            'Close Short': [
                'button:has-text("Close Short")',
                'text=Close Short',
                'button:has-text("Close")',
                '.close-short-button',
                'button[class*="close"][class*="short"]'
            ]
        };

        const selectors = buttonMap[orderType];
        if (!selectors) {
            throw new Error(`Unknown order type: ${orderType}`);
        }

        // Try each selector with increasing timeout
        for (let i = 0; i < selectors.length; i++) {
            const selector = selectors[i];
            try {
                const button = this.page.locator(selector).first();
                const timeout = i === 0 ? 2000 : 1000;

                if (await button.isVisible({ timeout })) {
                    await button.click();
                    console.log(`✅ ${orderType} button clicked using selector: ${selector}`);
                    return;
                }
            } catch (error) {
                console.log(`⚠️ Selector ${i + 1} failed: ${selector}`);
                continue;
            }
        }

        // If button not found, try force tab switching and retry
        console.log(`⚠️ ${orderType} button not found, trying force tab switch...`);
        try {
            await this.forceTabSwitch(orderType);

            // Retry button search after force tab switch
            for (let i = 0; i < Math.min(selectors.length, 2); i++) {
                const selector = selectors[i];
                try {
                    const button = this.page.locator(selector).first();
                    if (await button.isVisible({ timeout: 1000 })) {
                        await button.click();
                        console.log(`✅ ${orderType} button clicked after force tab switch: ${selector}`);
                        return;
                    }
                } catch (error) {
                    continue;
                }
            }
        } catch (tabError) {
            console.log(`⚠️ Force tab switch failed: ${tabError.message}`);
        }

        if (orderType.includes('Close')) {
            throw new Error(`${orderType} button not found. This usually means there are no open ${orderType.includes('Long') ? 'long' : 'short'} positions to close.`);
        }

        throw new Error(`${orderType} button not found`);
    }

    async checkForOpenPositions() {
        console.log('🔍 Checking for open positions...');

        try {
            const positionIndicators = [
                '.position-row',
                '.open-position',
                '[class*="position"]',
                'text=Position',
                'text=PnL',
                'text=Unrealized'
            ];

            for (const indicator of positionIndicators) {
                try {
                    const element = this.page.locator(indicator).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`✅ Found position indicator: ${indicator}`);
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }

            const closeButtons = await this.page.locator('button:has-text("Close Long"), button:has-text("Close Short")').all();
            if (closeButtons.length > 0) {
                for (const btn of closeButtons) {
                    if (await btn.isVisible({ timeout: 100 })) {
                        console.log('✅ Found close buttons - positions exist');
                        return true;
                    }
                }
            }

            console.log('⚠️ No open positions found');
            return false;
        } catch (error) {
            console.log('⚠️ Error checking positions:', error.message);
            return false;
        }
    }

    async quickErrorCheck(orderType) {
        console.log(`🔍 Quick error check for ${orderType}...`);

        // Only check for critical errors that should cause us to skip the trade
        const errorSelectors = [
            'text=Insufficient closeable quantity',
            'text=insufficient closeable quantity',
            'text=Insufficient closeable quantity!',
            'text=No position to close',
            'text=Position not found',
            '[class*="error"]:has-text("closeable")',
            '[class*="warning"]:has-text("closeable")'
        ];

        for (const selector of errorSelectors) {
            try {
                const errorElement = this.page.locator(selector).first();
                if (await errorElement.isVisible({ timeout: 100 })) {
                    const errorText = await errorElement.textContent();
                    console.log(`⚠️ Error detected: "${errorText}"`);

                    // Try to close the error popup quickly
                    const closeButtons = [
                        'button:has-text("Close")',
                        'button:has-text("×")',
                        '.close-btn',
                        '[data-testid*="close"]'
                    ];

                    for (const closeBtn of closeButtons) {
                        try {
                            const closeElement = this.page.locator(closeBtn).first();
                            if (await closeElement.isVisible({ timeout: 100 })) {
                                await closeElement.click();
                                console.log('✅ Closed error popup');
                                break;
                            }
                        } catch (e) {
                            // Continue
                        }
                    }

                    // Throw special error to skip this trade
                    throw new Error(`SKIP_TRADE: ${errorText}`);
                }
            } catch (error) {
                if (error.message.startsWith('SKIP_TRADE:')) {
                    throw error; // Re-throw skip errors
                }
                // Continue checking other selectors
            }
        }

        console.log('✅ No critical errors detected');
        return true;
    }

    async handlePopup(orderType) {
        // DEPRECATED: This function is no longer used for MEXC futures trading
        // as there are no popups for Open Long/Short and Close Long/Short operations.
        // Keeping for backward compatibility but should not be called.
        console.log(`⚠️ handlePopup called for ${orderType} - this should not happen for MEXC futures`);
        return true;
    }

    async verifySuccess() {
        try {
            const successSelectors = [
                'text=Purchased successfully',
                'text=Success',
                'text=success',
                '.success'
            ];

            for (const selector of successSelectors) {
                try {
                    if (await this.page.locator(selector).first().isVisible({ timeout: 500 })) {
                        return true;
                    }
                } catch (error) {
                    continue;
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    async clearInputField(input) {
        try {
            await input.click();
            await this.page.waitForTimeout(50);

            await input.press('Control+a');
            await this.page.waitForTimeout(50);
            await input.press('Delete');
            await this.page.waitForTimeout(50);

            await input.fill('');
            await this.page.waitForTimeout(50);

            const value = await input.inputValue();
            if (value && value.trim() !== '') {
                await input.click();
                for (let i = 0; i < 20; i++) {
                    await input.press('Backspace');
                }
                await input.fill('');
            }

            console.log('✅ Input field cleared');
        } catch (error) {
            console.log('⚠️ Error clearing input field:', error.message);
        }
    }

    async clearQuantityFields() {
        console.log('🔢 Clearing quantity fields...');

        const clearStrategies = [
            async () => {
                const input = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
                if (await input.isVisible({ timeout: 300 })) {
                    await this.clearInputField(input);
                    return true;
                }
                return false;
            },
            async () => {
                const selectors = ['input[type="text"]', 'input[type="number"]'];
                for (const selector of selectors) {
                    const inputs = await this.page.locator(selector).all();
                    for (const input of inputs) {
                        if (await input.isVisible({ timeout: 100 })) {
                            try {
                                const currentValue = await input.inputValue();
                                if (currentValue && currentValue.trim() !== '') {
                                    await this.clearInputField(input);
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                }
                return true;
            }
        ];

        for (let i = 0; i < clearStrategies.length; i++) {
            try {
                if (await clearStrategies[i]()) {
                    console.log(`✅ Fields cleared using strategy ${i + 1}`);
                    break;
                }
            } catch (error) {
                continue;
            }
        }
    }

    async closePersistentPopups() {
        console.log('🚨 Closing persistent popups...');

        try {
            const modalWrapSelectors = [
                '.ant-modal-wrap.ant-modal-wrap-footer-custom',
                '.ant-modal-wrap',
                '.modal-wrap'
            ];

            for (const selector of modalWrapSelectors) {
                try {
                    const modalWrap = this.page.locator(selector).first();
                    if (await modalWrap.isVisible({ timeout: 200 })) {
                        const closeSelectors = [
                            'button:has-text("Close")',
                            'button:has-text("Cancel")',
                            '.ant-modal-close'
                        ];

                        for (const closeSelector of closeSelectors) {
                            try {
                                const closeBtn = modalWrap.locator(closeSelector).first();
                                if (await closeBtn.isVisible({ timeout: 100 })) {
                                    await closeBtn.click();
                                    console.log(`✅ Closed modal via: ${closeSelector}`);
                                    await this.page.waitForTimeout(300);
                                    break;
                                }
                            } catch (error) {
                                continue;
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

            try {
                await this.page.keyboard.press('Escape');
                await this.page.waitForTimeout(200);
                console.log('✅ Pressed ESC to close any remaining modals');
            } catch (error) {
                // Continue
            }

        } catch (error) {
            console.log('⚠️ Error closing persistent popups:', error.message);
        }
    }

    async aggressiveCleanup() {
        console.log('🧹 Aggressive cleanup...');

        try {
            await this.closePersistentPopups();
            await this.clearQuantityFields();
            console.log('✅ Cleanup completed');
        } catch (error) {
            console.log('⚠️ Cleanup had issues, continuing...');
        }
    }

    async checkLoginRequired() {
        try {
            // Check for login button or login text
            const loginSelectors = [
                'button:has-text("Login")',
                'button:has-text("Log in")',
                'button:has-text("Sign in")',
                'a:has-text("Login")',
                'a:has-text("Log in")',
                'text=Sign up to get',
                'text=Login',
                'text=Log in'
            ];

            for (const selector of loginSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 500 })) {
                        console.log(`🚨 Login required detected: ${selector}`);
                        return true;
                    }
                } catch (error) {
                    // Continue checking other selectors
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Error checking login status:', error.message);
            return false;
        }
    }

    async sendTelegramAlert(title, message) {
        try {
            const fullMessage = `${title}\n\n${message}\n\nTime: ${new Date().toISOString()}`;

            await axios.post(`https://api.telegram.org/bot${this.telegramBotToken}/sendMessage`, {
                chat_id: this.telegramChatId,
                text: fullMessage,
                parse_mode: 'HTML'
            });

            console.log('📱 Telegram alert sent successfully');
        } catch (error) {
            console.log('❌ Failed to send Telegram alert:', error.message);
        }
    }

    async quickVerifySuccess(orderType) {
        try {
            // Quick verification with reduced timeout
            await this.page.waitForTimeout(500); // Reduced from longer waits

            // Check for success indicators
            const successSelectors = [
                'text=Order submitted',
                'text=Success',
                'text=Completed',
                '.success',
                '.order-success'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 300 })) {
                        return true;
                    }
                } catch (error) {
                    // Continue checking
                }
            }

            return true; // Assume success if no error indicators
        } catch (error) {
            console.log('⚠️ Quick verification failed:', error.message);
            return false;
        }
    }

    async fetchBalance() {
        try {
            console.log('💰 Fetching balance from MEXC frontend...');

            if (!this.page) {
                throw new Error('Browser not connected');
            }

            // Try multiple balance selectors for MEXC frontend
            const balanceSelectors = [
                'span.AssetsItem_num__9eLwJ',
                '.AssetsItem_num__9eLwJ',
                '[class*="AssetsItem_num"]',
                '[class*="assets"] [class*="num"]',
                'span:has-text("USDT")',
                'text=/\\d+\\.\\d+\\s*USDT/',
                '.balance-value',
                '.wallet-balance',
                '[data-testid*="balance"]'
            ];

            let balanceText = null;
            let usedSelector = null;

            for (const selector of balanceSelectors) {
                try {
                    const element = this.page.locator(selector).first();
                    if (await element.isVisible({ timeout: 1000 })) {
                        balanceText = await element.textContent();
                        usedSelector = selector;
                        console.log(`✅ Found balance with selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }

            if (!balanceText) {
                // Try to find any element containing USDT with numbers
                try {
                    const usdtElements = await this.page.locator('text=/\\d+\\.\\d+.*USDT/').all();
                    if (usdtElements.length > 0) {
                        balanceText = await usdtElements[0].textContent();
                        usedSelector = 'USDT pattern match';
                        console.log('✅ Found balance with USDT pattern match');
                    }
                } catch (error) {
                    // Continue
                }
            }

            if (balanceText) {
                // Extract numeric value from text like "‎2.2951 USDT" or "2.2951USDT"
                const balanceMatch = balanceText.match(/[\d,]+\.?\d*/);
                if (balanceMatch) {
                    const balance = parseFloat(balanceMatch[0].replace(/,/g, ''));

                    this.lastBalance = {
                        raw: balanceText.trim(),
                        value: balance,
                        currency: 'USDT',
                        selector: usedSelector,
                        timestamp: new Date().toISOString()
                    };
                    this.lastBalanceUpdate = Date.now();

                    console.log(`💰 Balance fetched: ${balance} USDT`);
                    console.log(`   Raw text: "${balanceText.trim()}"`);
                    console.log(`   Selector: ${usedSelector}`);

                    return this.lastBalance;
                } else {
                    console.log(`⚠️ Could not parse balance from: "${balanceText}"`);
                }
            } else {
                console.log('⚠️ No balance element found');
            }

            return null;

        } catch (error) {
            console.error('❌ Failed to fetch balance:', error.message);
            return null;
        }
    }

    async getBalance(forceRefresh = false) {
        try {
            // Return cached balance if recent (less than 30 seconds old)
            if (!forceRefresh && this.lastBalance && this.lastBalanceUpdate) {
                const age = Date.now() - this.lastBalanceUpdate;
                if (age < 30000) { // 30 seconds
                    console.log(`💰 Using cached balance: ${this.lastBalance.value} USDT (${Math.round(age/1000)}s old)`);
                    return this.lastBalance;
                }
            }

            // Fetch fresh balance
            return await this.fetchBalance();

        } catch (error) {
            console.error('❌ Failed to get balance:', error.message);
            return this.lastBalance; // Return cached balance as fallback
        }
    }

    async disconnect() {
        try {
            if (this.browser) {
                await this.browser.close();
                console.log('✅ Browser connection closed');
            }
        } catch (error) {
            console.log('⚠️ Error closing browser:', error.message);
        }
    }
}

module.exports = MexcFuturesTrader;
